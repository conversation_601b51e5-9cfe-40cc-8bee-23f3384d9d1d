'use client';

import { createContext, useContext, useEffect, useState, type ReactNode, useCallback, useRef } from 'react';
import type { SessionRecord, TimerMode } from '@/types';
import { useSettings } from './settings-provider';
import * as Tone from 'tone';
import { useLanguage } from './language-provider';
import { HISTORY_KEY } from '@/lib/constants';

type TimerProviderState = {
  mode: TimerMode;
  timeLeft: number;
  isActive: boolean;
  sessionCount: number;
  isAutoStarting: boolean;
  startTimer: () => void;
  pauseTimer: () => void;
  skipTimer: () => void;
  addMinute: () => void;
};

const TimerProviderContext = createContext<TimerProviderState | undefined>(undefined);

export function TimerProvider({ children }: { children: ReactNode }) {
  const { settings, isLoaded } = useSettings();
  const { t } = useLanguage();
  const [mode, setMode] = useState<TimerMode>('focus');
  const [timeLeft, setTimeLeft] = useState(settings.focusDuration * 60);
  const [isActive, setIsActive] = useState(false);
  const [sessionCount, setSessionCount] = useState(0);
  const [isAutoStarting, setIsAutoStarting] = useState(false);

  const intervalRef = useRef<NodeJS.Timeout | null>(null);

  const playSound = (url: string) => {
    try {
      const player = new Tone.Player(url).toDestination();
      Tone.loaded().then(() => player.start());
    } catch (e) { console.error(e) }
  };

  const showNotification = (title: string, body: string) => {
    if (settings.notifications.enabled && Notification.permission === 'granted') {
      navigator.serviceWorker.ready.then(registration => {
        registration.showNotification(title, { body, icon: '/icon-192x192.png' });
      });
    }
  };

  const saveSession = useCallback((type: TimerMode, duration: number) => {
    const newSession: SessionRecord = {
      id: new Date().toISOString(),
      type,
      duration,
      completedAt: new Date().toISOString(),
    };
    try {
      const history = JSON.parse(localStorage.getItem(HISTORY_KEY) || '[]') as SessionRecord[];
      history.unshift(newSession);
      localStorage.setItem(HISTORY_KEY, JSON.stringify(history.slice(0, 100))); // Limit history size
    } catch (e) { console.error('Failed to save session', e)}
  }, []);

  const nextMode = useCallback(() => {
    setIsActive(false);

    const completedMode = mode;
    const completedDuration =
      completedMode === 'focus' ? settings.focusDuration :
      completedMode === 'shortBreak' ? settings.shortBreakDuration :
      settings.longBreakDuration;
    saveSession(completedMode, completedDuration);

    if (completedMode === 'focus') {
      playSound(settings.sounds.focusEnd);
      showNotification(t('notification.focus.title'), t('notification.focus.body'));
      const newSessionCount = sessionCount + 1;
      setSessionCount(newSessionCount);
      if (newSessionCount % settings.sessionsBeforeLongBreak === 0) {
        setMode('longBreak');
        setTimeLeft(settings.longBreakDuration * 60);
      } else {
        setMode('shortBreak');
        setTimeLeft(settings.shortBreakDuration * 60);
      }
    } else {
      playSound(settings.sounds.shortBreakEnd);
      showNotification(t('notification.break.title'), t('notification.break.body'));
      setMode('focus');
      setTimeLeft(settings.focusDuration * 60);
    }

    // Auto-start next period if enabled
    if (settings.autoStartNextPeriod) {
      setIsAutoStarting(true);
      setTimeout(() => {
        setIsAutoStarting(false);
        setIsActive(true);
      }, 1000); // 1 second delay to show auto-start indicator
    }
  }, [mode, sessionCount, settings, t, saveSession]);

  useEffect(() => {
    if (isActive) {
      intervalRef.current = setInterval(() => {
        setTimeLeft(prev => {
          if (prev <= 1) {
            clearInterval(intervalRef.current!);
            nextMode();
            return 0;
          }
          return prev - 1;
        });
      }, 1000);
    } else {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    }
    return () => {
      if (intervalRef.current) clearInterval(intervalRef.current);
    };
  }, [isActive, nextMode]);

  useEffect(() => {
    if (isLoaded) {
      setIsActive(false);
      setMode('focus');
      setTimeLeft(settings.focusDuration * 60);
    }
  }, [settings.focusDuration, isLoaded]);

  const startTimer = () => {
    Tone.start();
    setIsActive(true);
  };
  const pauseTimer = () => setIsActive(false);
  const skipTimer = () => nextMode();
  const addMinute = () => setTimeLeft(prev => prev + 60);

  const value = { mode, timeLeft, isActive, sessionCount, isAutoStarting, startTimer, pauseTimer, skipTimer, addMinute };

  return <TimerProviderContext.Provider value={value}>{children}</TimerProviderContext.Provider>;
}

export const useTimer = () => {
  const context = useContext(TimerProviderContext);
  if (context === undefined) {
    throw new Error('useTimer must be used within a TimerProvider');
  }
  return context;
};
