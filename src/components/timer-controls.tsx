'use client';

import { useTimer } from '@/providers/timer-provider';
import { useLanguage } from '@/providers/language-provider';
import { Button } from './ui/button';
import { Play, Pause, SkipForward } from 'lucide-react';
import { cn } from '@/lib/utils';
import { PlusOneIcon } from './icons/plus-one';
import { AutoStartToggle } from './auto-start-toggle';

export function TimerControls() {
  const { isActive, isAutoStarting, startTimer, pauseTimer, skipTimer, addMinute } = useTimer();
  const { t } = useLanguage();

  const buttonClasses = "rounded-full w-20 h-20 md:w-24 md:h-24 landscape:w-16 landscape:h-16 text-base md:text-lg landscape:text-sm";
  const iconClasses = "w-8 h-8 md:w-10 md:h-10 landscape:w-6 landscape:h-6";
  const smallIconClasses = "w-6 h-6 md:w-8 md:h-8 landscape:w-5 landscape:h-5";
  const smallButtonClasses = "w-14 h-14 md:w-16 md:h-16 landscape:w-12 landscape:h-12 rounded-full";
  const plusOneIconClasses = "w-8 h-8 md:w-8 md:h-8 landscape:w-6 landscape:h-6";


  return (
    <div className="flex flex-col items-center gap-2 landscape:gap-1">
      <div className="flex items-center justify-center gap-2 landscape:gap-3">
        {isActive ? (
          <Button onClick={pauseTimer} size="lg" className={cn(buttonClasses)}>
            <Pause className={cn(iconClasses)} />
            <span className="sr-only">{t('controls.pause')}</span>
          </Button>
        ) : (
          <Button
            onClick={startTimer}
            size="lg"
            className={cn(
              buttonClasses,
              isAutoStarting && "animate-pulse bg-primary/80"
            )}
            disabled={isAutoStarting}
          >
            <Play className={cn(iconClasses)} />
            <span className="sr-only">{t('controls.start')}</span>
          </Button>
        )}

        <Button onClick={skipTimer} variant="outline" size="icon" className={cn(smallButtonClasses)}>
          <SkipForward className={cn(smallIconClasses)} />
          <span className="sr-only">{t('controls.skip')}</span>
        </Button>

        <Button onClick={addMinute} variant="outline" size="icon" className={cn(smallButtonClasses)}>
          <PlusOneIcon className={cn(plusOneIconClasses)} />
          <span className="sr-only">{t('controls.add_minute')}</span>
        </Button>
      </div>

      <div className="flex flex-col items-center gap-1">
        {isAutoStarting && (
          <div className="text-sm text-muted-foreground animate-pulse landscape:text-xs">
            {t('controls.auto_starting')}
          </div>
        )}
        <AutoStartToggle />
      </div>
    </div>
  );
}
