'use client';

import { useSettings } from '@/providers/settings-provider';
import { useLanguage } from '@/providers/language-provider';
import { Switch } from './ui/switch';
import { Label } from './ui/label';
import { cn } from '@/lib/utils';
import { RotateCcw } from 'lucide-react';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from './ui/tooltip';

export function AutoStartToggle() {
  const { settings, setSettings } = useSettings();
  const { t } = useLanguage();

  const handleToggle = (checked: boolean) => {
    setSettings({ autoStartNextPeriod: checked });
  };

  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <div className="flex items-center space-x-1.5 p-1.5 rounded-md bg-card/40 backdrop-blur-sm border border-border/30 shadow-sm hover:bg-card/60 transition-colors cursor-pointer">
            <RotateCcw className="w-3.5 h-3.5 text-muted-foreground" />
            <Switch
              id="auto-start"
              checked={settings.autoStartNextPeriod}
              onCheckedChange={handleToggle}
              className="data-[state=checked]:bg-primary data-[state=unchecked]:bg-input scale-75"
            />
            <Label
              htmlFor="auto-start"
              className="sr-only"
            >
              {t('controls.auto_start')}
            </Label>
          </div>
        </TooltipTrigger>
        <TooltipContent>
          <p>{t('controls.auto_start')}</p>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
}
